/* Custom CSS for Neetel Website */

:root {
    --primary-color: #28a745;
    --primary-dark: #1e7e34;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --purple-color: #6f42c1;
    --dark-color: #212529;
    --light-color: #f8f9fa;
    --green-light: #d4edda;
    --green-gradient: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.text-purple {
    color: var(--purple-color) !important;
}

/* Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
    color: var(--dark-color) !important;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
}

.navbar-toggler {
    border-color: var(--primary-color);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 100px 0 50px 0;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.hero-image {
    text-align: center;
    position: relative;
    z-index: 2;
    padding: 2rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image img {
    max-width: 100%;
    width: auto;
    height: auto;
    filter: drop-shadow(0 15px 35px rgba(40, 167, 69, 0.3));
    animation: float 6s ease-in-out infinite;
    transition: transform 0.3s ease;
    border-radius: 20px;
}

.hero-image img:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 20px 40px rgba(40, 167, 69, 0.4));
}

/* Hero content styling */
.hero-section .container .row {
    align-items: center;
    min-height: 70vh;
}

.hero-section h1 {
    font-size: 3.5rem;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-section .lead {
    font-size: 1.3rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Service Cards */
.service-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--green-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.service-card h4 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.service-card p {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

/* Feature Cards */
.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.feature-card h5 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.feature-card p {
    color: var(--secondary-color);
}

/* BPO Feature Styles */
.bpo-feature, .data-entry-feature, .kpo-feature {
    text-align: center;
    padding: 2rem;
}

.bpo-feature h4, .data-entry-feature h4, .kpo-feature h4 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.bpo-feature ul li, .data-entry-feature ul li, .kpo-feature ul li {
    padding: 0.5rem 0;
    font-size: 1.1rem;
}

/* Service Items */
.service-item {
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.service-item:hover {
    background: var(--light-color);
    transform: translateX(5px);
}

.bpo-services, .data-services, .kpo-services {
    padding: 2rem;
}

/* Footer Styles */
footer {
    background: var(--dark-color) !important;
}

footer h5 {
    color: white;
    margin-bottom: 1rem;
    font-weight: 600;
}

footer p, footer li {
    color: #adb5bd;
}

footer a {
    color: #adb5bd;
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--warning-color);
}

.social-links a {
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 120px 0 50px 0;
        min-height: 80vh;
    }

    .hero-image {
        margin-top: 2rem;
        padding: 1rem 0;
    }

    .hero-image img {
        width: 300px;
        max-width: 90%;
    }

    .display-4 {
        font-size: 2rem;
    }

    .service-card, .feature-card {
        margin-bottom: 2rem;
    }

    .bpo-feature, .data-entry-feature, .kpo-feature {
        text-align: center;
        margin-bottom: 2rem;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .hero-image img {
        width: 400px;
    }
}

@media (min-width: 1025px) {
    .hero-image img {
        width: auto;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Button Hover Effects */
.btn {
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Contact Page Styles */
.contact-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.contact-form {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-form .form-control, .contact-form .form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus, .contact-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.business-hours {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Section Spacing */
section {
    padding: 5rem 0;
}

@media (max-width: 768px) {
    section {
        padding: 3rem 0;
    }

    .contact-form {
        padding: 2rem;
    }
}

/* Service Detail Cards */
.service-detail-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.service-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.service-detail-card img {
    transition: transform 0.3s ease;
}

.service-detail-card:hover img {
    transform: scale(1.05);
}

/* Technology Items */
.tech-item {
    padding: 1rem;
    transition: all 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-5px);
}

.tech-item i {
    transition: all 0.3s ease;
}

.tech-item:hover i {
    transform: scale(1.2);
}

/* Process Steps */
.process-steps {
    position: relative;
}

.process-step {
    display: flex;
    align-items: flex-start;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--green-gradient);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h6 {
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.step-content p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Page-specific styles */
.bg-primary {
    background: var(--green-gradient) !important;
}

/* Additional green theme elements */
.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
}

.text-primary {
    color: var(--primary-color) !important;
}
