# Neetel Website

A modern, responsive website for Neetel - Technology & Business Solutions company.

## Features

- **Responsive Design**: Built with Bootstrap 5 for mobile-first responsive design
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Interactive Elements**: Smooth scrolling, hover effects, and form validation
- **SEO Optimized**: Proper meta tags and semantic HTML structure

## Services Showcased

### Development Services
- Website Development
- Mobile App Development  
- Desktop App Development
- IoT Solutions
- Custom Software for Businesses

### Business Solutions
- ERP Systems
- CRM Systems
- Inventory Management
- POS Systems

### BPO Services
- Business Process Outsourcing
- Administrative Support
- Document Processing
- Back-office Operations

### Call Center Services
- Inbound & Outbound Customer Support
- Lead Generation
- Appointment Setting
- Multilingual Support

### Data Entry & Management
- Accurate and Fast Data Entry Services
- Form Filling, Conversion, and Processing
- Offline/Online Data Management

### KPO Services
- Market Research & Analysis
- Financial & Legal Process Outsourcing
- High-end Knowledge-based Solutions

## Technologies Used

- **HTML5**: Semantic markup and modern web standards
- **CSS3**: Custom styling with CSS Grid and Flexbox
- **Bootstrap 5**: Responsive framework and components
- **JavaScript**: Interactive functionality and form validation
- **Font Awesome**: Professional icons
- **AOS Library**: Scroll animations

## File Structure

```
neetel/
├── index.html          # Homepage
├── about.html          # About page
├── contact.html        # Contact page
├── css/
│   └── style.css       # Custom styles
├── js/
│   └── script.js       # JavaScript functionality
├── images/
│   └── neetel_logo.png # Company logo
└── README.md           # This file
```

## Contact Information

- **Email**: <EMAIL>
- **Phone**: 9697550781
- **Website**: www.neetel.com | www.neetel.in
- **Location**: MD Heights near jeel bridge Singhpora Pattan

## Getting Started

1. Open `index.html` in a web browser
2. Navigate through the different sections and pages
3. Test the contact form functionality
4. Verify responsive design on different screen sizes

## Browser Compatibility

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Future Enhancements

- Add portfolio/case studies section
- Implement actual form submission backend
- Add testimonials section
- Include team member profiles
- Add blog/news section
