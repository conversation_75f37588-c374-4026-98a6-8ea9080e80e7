<svg viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient definitions -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f0c29;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#24243e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#302b63;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffb347;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </radialGradient>
    
    <!-- Glow filter -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Drop shadow -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="600" fill="url(#bgGradient)"/>
  
  <!-- Floating particles -->
  <g opacity="0.6">
    <circle cx="100" cy="150" r="2" fill="url(#accentGradient)">
      <animate attributeName="cy" values="150; 100; 150" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.6; 1; 0.6" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="200" cy="450" r="1.5" fill="url(#goldGradient)">
      <animate attributeName="cy" values="450; 400; 450" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4; 0.8; 0.4" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1000" cy="200" r="2.5" fill="url(#primaryGradient)">
      <animate attributeName="cy" values="200; 150; 200" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5; 1; 0.5" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Central hub/brain -->
  <g transform="translate(600, 300)">
    <!-- Main central circle -->
    <circle cx="0" cy="0" r="40" fill="url(#primaryGradient)" filter="url(#glow)">
      <animate attributeName="r" values="40; 45; 40" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="25" fill="none" stroke="url(#accentGradient)" stroke-width="2" opacity="0.7">
      <animate attributeName="r" values="25; 30; 25" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Inner rotating elements -->
    <g>
      <circle cx="15" cy="0" r="4" fill="url(#goldGradient)" opacity="0.8"/>
      <circle cx="-15" cy="0" r="4" fill="url(#goldGradient)" opacity="0.8"/>
      <circle cx="0" cy="15" r="4" fill="url(#goldGradient)" opacity="0.8"/>
      <circle cx="0" cy="-15" r="4" fill="url(#goldGradient)" opacity="0.8"/>
      <animateTransform attributeName="transform" type="rotate" values="0; 360" dur="8s" repeatCount="indefinite"/>
    </g>
  </g>
  
  <!-- Connected nodes network -->
  <g opacity="0.8">
    <!-- Node 1: Cloud Computing -->
    <g transform="translate(200, 150)">
      <circle cx="0" cy="0" r="30" fill="url(#accentGradient)" opacity="0.3" filter="url(#shadow)">
        <animate attributeName="opacity" values="0.3; 0.6; 0.3" dur="4s" repeatCount="indefinite"/>
      </circle>
      <!-- Cloud shape -->
      <ellipse cx="0" cy="0" rx="18" ry="10" fill="url(#accentGradient)"/>
      <ellipse cx="-8" cy="-3" rx="10" ry="7" fill="url(#accentGradient)"/>
      <ellipse cx="8" cy="-3" rx="10" ry="7" fill="url(#accentGradient)"/>
    </g>
    
    <!-- Node 2: Analytics/Data -->
    <g transform="translate(350, 450)">
      <circle cx="0" cy="0" r="25" fill="url(#primaryGradient)" opacity="0.3" filter="url(#shadow)">
        <animate attributeName="opacity" values="0.3; 0.5; 0.3" dur="3s" repeatCount="indefinite"/>
      </circle>
      <!-- Bar chart -->
      <rect x="-12" y="5" width="4" height="8" fill="url(#goldGradient)"/>
      <rect x="-6" y="0" width="4" height="13" fill="url(#goldGradient)"/>
      <rect x="0" y="3" width="4" height="10" fill="url(#goldGradient)"/>
      <rect x="6" y="-2" width="4" height="15" fill="url(#goldGradient)"/>
    </g>
    
    <!-- Node 3: Server/Infrastructure -->
    <g transform="translate(900, 180)">
      <circle cx="0" cy="0" r="28" fill="url(#goldGradient)" opacity="0.3" filter="url(#shadow)">
        <animate attributeName="opacity" values="0.3; 0.6; 0.3" dur="5s" repeatCount="indefinite"/>
      </circle>
      <!-- Server icon -->
      <rect x="-12" y="-8" width="24" height="6" rx="2" fill="none" stroke="url(#accentGradient)" stroke-width="2"/>
      <rect x="-12" y="0" width="24" height="6" rx="2" fill="none" stroke="url(#accentGradient)" stroke-width="2"/>
      <circle cx="-8" cy="-5" r="1" fill="url(#accentGradient)"/>
      <circle cx="-8" cy="3" r="1" fill="url(#accentGradient)"/>
    </g>
    
    <!-- Node 4: Mobile/Devices -->
    <g transform="translate(1000, 420)">
      <circle cx="0" cy="0" r="26" fill="url(#accentGradient)" opacity="0.3" filter="url(#shadow)">
        <animate attributeName="opacity" values="0.3; 0.5; 0.3" dur="3.5s" repeatCount="indefinite"/>
      </circle>
      <!-- Mobile device -->
      <rect x="-6" y="-10" width="12" height="20" rx="3" fill="none" stroke="url(#primaryGradient)" stroke-width="2"/>
      <rect x="-4" y="-7" width="8" height="12" fill="url(#primaryGradient)" opacity="0.3"/>
      <circle cx="0" cy="6" r="2" fill="url(#primaryGradient)"/>
    </g>
    
    <!-- Node 5: Security/Shield -->
    <g transform="translate(150, 380)">
      <circle cx="0" cy="0" r="24" fill="url(#primaryGradient)" opacity="0.3" filter="url(#shadow)">
        <animate attributeName="opacity" values="0.3; 0.6; 0.3" dur="4.5s" repeatCount="indefinite"/>
      </circle>
      <!-- Shield shape -->
      <path d="M 0,-12 L 8,-8 L 8,8 L 0,12 L -8,8 L -8,-8 Z" fill="url(#goldGradient)" opacity="0.8"/>
      <path d="M 0,-8 L 4,-6 L 4,4 L 0,8 L -4,4 L -4,-6 Z" fill="none" stroke="url(#accentGradient)" stroke-width="1.5"/>
    </g>
  </g>
  
  <!-- Animated connections -->
  <g opacity="0.6">
    <!-- Connection 1: Central hub to cloud -->
    <line x1="600" y1="300" x2="200" y2="150" stroke="url(#accentGradient)" stroke-width="2">
      <animate attributeName="stroke-dasharray" values="0 400; 200 200; 400 0" dur="4s" repeatCount="indefinite"/>
    </line>
    
    <!-- Connection 2: Central hub to analytics -->
    <line x1="600" y1="300" x2="350" y2="450" stroke="url(#primaryGradient)" stroke-width="2">
      <animate attributeName="stroke-dasharray" values="0 300; 150 150; 300 0" dur="5s" repeatCount="indefinite"/>
    </line>
    
    <!-- Connection 3: Central hub to server -->
    <line x1="600" y1="300" x2="900" y2="180" stroke="url(#goldGradient)" stroke-width="2">
      <animate attributeName="stroke-dasharray" values="0 350; 175 175; 350 0" dur="3.5s" repeatCount="indefinite"/>
    </line>
    
    <!-- Connection 4: Central hub to mobile -->
    <line x1="600" y1="300" x2="1000" y2="420" stroke="url(#accentGradient)" stroke-width="2">
      <animate attributeName="stroke-dasharray" values="0 450; 225 225; 450 0" dur="4.5s" repeatCount="indefinite"/>
    </line>
    
    <!-- Connection 5: Central hub to security -->
    <line x1="600" y1="300" x2="150" y2="380" stroke="url(#primaryGradient)" stroke-width="2">
      <animate attributeName="stroke-dasharray" values="0 500; 250 250; 500 0" dur="6s" repeatCount="indefinite"/>
    </line>
    
    <!-- Inter-node connections -->
    <line x1="200" y1="150" x2="900" y2="180" stroke="url(#accentGradient)" stroke-width="1" opacity="0.4">
      <animate attributeName="opacity" values="0.4; 0.8; 0.4" dur="7s" repeatCount="indefinite"/>
    </line>
    <line x1="350" y1="450" x2="1000" y2="420" stroke="url(#goldGradient)" stroke-width="1" opacity="0.3">
      <animate attributeName="opacity" values="0.3; 0.7; 0.3" dur="8s" repeatCount="indefinite"/>
    </line>
  </g>
  
  <!-- Data flow particles -->
  <g>
    <!-- Flowing particles on connections -->
    <circle cx="200" cy="150" r="3" fill="url(#accentGradient)" opacity="0.8">
      <animateMotion dur="4s" repeatCount="indefinite">
        <path d="M 0,0 Q 400,75 400,150"/>
      </animateMotion>
      <animate attributeName="opacity" values="0; 1; 0" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="350" cy="450" r="2.5" fill="url(#goldGradient)" opacity="0.9">
      <animateMotion dur="5s" repeatCount="indefinite">
        <path d="M 0,0 Q 475,-75 250,-150"/>
      </animateMotion>
      <animate attributeName="opacity" values="0; 1; 0" dur="5s" repeatCount="indefinite"/>
    </circle>
    
    <circle cx="900" cy="180" r="3.5" fill="url(#primaryGradient)" opacity="0.7">
      <animateMotion dur="3.5s" repeatCount="indefinite">
        <path d="M 0,0 Q -150,60 -300,120"/>
      </animateMotion>
      <animate attributeName="opacity" values="0; 1; 0" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Background geometric elements -->
  <g opacity="0.1">
    <polygon points="50,50 100,50 75,100" fill="url(#accentGradient)">
      <animateTransform attributeName="transform" type="rotate" values="0 75 66; 360 75 66" dur="20s" repeatCount="indefinite"/>
    </polygon>
    <rect x="1100" y="500" width="40" height="40" fill="url(#primaryGradient)" transform="rotate(45 1120 520)">
      <animateTransform attributeName="transform" type="rotate" values="45 1120 520; 405 1120 520" dur="15s" repeatCount="indefinite"/>
    </rect>
    <circle cx="1050" cy="80" r="15" fill="url(#goldGradient)">
      <animate attributeName="r" values="15; 20; 15" dur="6s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Pulsing glow effects -->
  <g opacity="0.3">
    <circle cx="600" cy="300" r="80" fill="url(#glowGradient)">
      <animate attributeName="r" values="80; 120; 80" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3; 0.1; 0.3" dur="4s" repeatCount="indefinite"/>
    </circle>
  </g>
  
</svg>